const express = require('express');
const { body } = require('express-validator');

const amcController = require('../controllers/amcController');
const { protect, restrictTo, restrictToCompany } = require('../middleware/auth');
const { validateRequest } = require('../middleware/validation');

const router = express.Router();

// Protect all routes
router.use(protect);
router.use(restrictToCompany);

// Validation rules
const createAMCValidation = [
  body('customer_id')
    .isInt({ min: 1 })
    .withMessage('Customer ID is required and must be a valid number'),
  body('start_date')
    .isISO8601()
    .withMessage('Start date is required and must be a valid date'),
  body('end_date')
    .isISO8601()
    .withMessage('End date is required and must be a valid date'),
  body('amount')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Amount must be a positive number')
];

const updateAMCValidation = [
  body('status')
    .optional()
    .isIn(['draft', 'active', 'expired', 'cancelled', 'completed'])
    .withMessage('Invalid status value'),
  body('amount')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Amount must be a positive number')
];

// Routes
router
  .route('/')
  .get(amcController.getAMCs)
  .post(createAMCValidation, validateRequest, amcController.createAMC);

router
  .route('/:id')
  .get(amcController.getAMC)
  .put(updateAMCValidation, validateRequest, amcController.updateAMC)
  .delete(restrictTo('admin', 'sub_admin'), amcController.deleteAMC);

module.exports = router;
