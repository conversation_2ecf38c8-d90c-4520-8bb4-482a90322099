#!/bin/bash

# Track New Production Deployment Script
# This script handles the complete deployment process

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
COMPOSE_FILE="docker-compose.production.yml"
ENV_FILE=".env.production"
BACKUP_DIR="./backups"
LOG_FILE="./deployment.log"

# Functions
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$LOG_FILE"
    exit 1
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$LOG_FILE"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$LOG_FILE"
}

# Check prerequisites
check_prerequisites() {
    log "Checking prerequisites..."
    
    # Check if Docker is installed
    if ! command -v docker &> /dev/null; then
        error "Docker is not installed. Please install Docker first."
    fi
    
    # Check if Docker Compose is installed
    if ! command -v docker-compose &> /dev/null; then
        error "Docker Compose is not installed. Please install Docker Compose first."
    fi
    
    # Check if environment file exists
    if [ ! -f "$ENV_FILE" ]; then
        error "Environment file $ENV_FILE not found. Please create it first."
    fi
    
    # Check if compose file exists
    if [ ! -f "$COMPOSE_FILE" ]; then
        error "Docker Compose file $COMPOSE_FILE not found."
    fi
    
    success "Prerequisites check passed"
}

# Create backup
create_backup() {
    log "Creating backup..."
    
    # Create backup directory if it doesn't exist
    mkdir -p "$BACKUP_DIR"
    
    # Backup database if container is running
    if docker-compose -f "$COMPOSE_FILE" ps mysql | grep -q "Up"; then
        BACKUP_FILE="$BACKUP_DIR/database_backup_$(date +%Y%m%d_%H%M%S).sql"
        docker-compose -f "$COMPOSE_FILE" exec -T mysql mysqldump -u root -p"$MYSQL_ROOT_PASSWORD" tracknew_production > "$BACKUP_FILE"
        success "Database backup created: $BACKUP_FILE"
    else
        warning "MySQL container not running, skipping database backup"
    fi
    
    # Backup uploaded files
    if [ -d "./uploads" ]; then
        UPLOADS_BACKUP="$BACKUP_DIR/uploads_backup_$(date +%Y%m%d_%H%M%S).tar.gz"
        tar -czf "$UPLOADS_BACKUP" ./uploads
        success "Uploads backup created: $UPLOADS_BACKUP"
    fi
}

# Build and deploy
deploy() {
    log "Starting deployment..."
    
    # Pull latest images
    log "Pulling latest images..."
    docker-compose -f "$COMPOSE_FILE" pull
    
    # Build custom images
    log "Building application images..."
    docker-compose -f "$COMPOSE_FILE" build --no-cache
    
    # Stop existing containers
    log "Stopping existing containers..."
    docker-compose -f "$COMPOSE_FILE" down
    
    # Start new containers
    log "Starting new containers..."
    docker-compose -f "$COMPOSE_FILE" up -d
    
    # Wait for services to be ready
    log "Waiting for services to be ready..."
    sleep 30
    
    # Run database migrations
    log "Running database migrations..."
    docker-compose -f "$COMPOSE_FILE" exec -T backend npm run migrate
    
    # Run database seeders (only on first deployment)
    if [ "$1" = "--seed" ]; then
        log "Running database seeders..."
        docker-compose -f "$COMPOSE_FILE" exec -T backend npm run seed
    fi
    
    success "Deployment completed successfully"
}

# Health check
health_check() {
    log "Performing health check..."
    
    # Check if all containers are running
    if ! docker-compose -f "$COMPOSE_FILE" ps | grep -q "Up"; then
        error "Some containers are not running"
    fi
    
    # Check backend health
    if ! curl -f http://localhost:8000/api/health > /dev/null 2>&1; then
        error "Backend health check failed"
    fi
    
    # Check frontend
    if ! curl -f http://localhost:3000/health > /dev/null 2>&1; then
        error "Frontend health check failed"
    fi
    
    success "Health check passed"
}

# Cleanup old images and containers
cleanup() {
    log "Cleaning up old images and containers..."
    
    # Remove unused images
    docker image prune -f
    
    # Remove unused containers
    docker container prune -f
    
    # Remove unused volumes (be careful with this)
    # docker volume prune -f
    
    success "Cleanup completed"
}

# Show usage
usage() {
    echo "Usage: $0 [OPTIONS]"
    echo "Options:"
    echo "  --seed          Run database seeders after deployment"
    echo "  --no-backup     Skip backup creation"
    echo "  --no-cleanup    Skip cleanup after deployment"
    echo "  --help          Show this help message"
}

# Main execution
main() {
    log "Starting Track New deployment process..."
    
    # Parse command line arguments
    SEED=false
    BACKUP=true
    CLEANUP=true
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --seed)
                SEED=true
                shift
                ;;
            --no-backup)
                BACKUP=false
                shift
                ;;
            --no-cleanup)
                CLEANUP=false
                shift
                ;;
            --help)
                usage
                exit 0
                ;;
            *)
                error "Unknown option: $1"
                ;;
        esac
    done
    
    # Execute deployment steps
    check_prerequisites
    
    if [ "$BACKUP" = true ]; then
        create_backup
    fi
    
    if [ "$SEED" = true ]; then
        deploy --seed
    else
        deploy
    fi
    
    health_check
    
    if [ "$CLEANUP" = true ]; then
        cleanup
    fi
    
    success "Track New deployment completed successfully!"
    log "Application is now running at:"
    log "  Frontend: http://localhost:3000"
    log "  Backend API: http://localhost:8000"
    log "  Database: localhost:3306"
}

# Run main function
main "$@"
