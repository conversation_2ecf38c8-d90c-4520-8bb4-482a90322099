#!/usr/bin/env node

/**
 * Track New - Laravel to Node.js Data Migration Script
 *
 * This script migrates data from the Laravel version to the new Node.js version
 *
 * Usage: node scripts/migrate-from-laravel.js
 */

const { Client } = require('pg');
const fs = require('fs').promises;
const path = require('path');
const bcrypt = require('bcryptjs');

// Configuration
const config = {
  source: {
    host: process.env.LARAVEL_DB_HOST || 'localhost',
    port: process.env.LARAVEL_DB_PORT || 3306,
    user: process.env.LARAVEL_DB_USER || 'root',
    password: process.env.LARAVEL_DB_PASSWORD || '',
    database: process.env.LARAVEL_DB_NAME || 'tracknew_laravel'
  },
  target: {
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 5432,
    user: process.env.DB_USER || 'tracknew_user',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME || 'tracknew_production'
  }
};

// Logging
const log = (message) => {
  console.log(`[${new Date().toISOString()}] ${message}`);
};

const error = (message) => {
  console.error(`[${new Date().toISOString()}] ERROR: ${message}`);
};

// Database connections
let sourceDb, targetDb;

// Migration statistics
const stats = {
  companies: 0,
  users: 0,
  customers: 0,
  services: 0,
  products: 0,
  sales: 0,
  invoices: 0,
  errors: 0
};

// Initialize database connections
async function initializeConnections() {
  try {
    log('Connecting to source database (Laravel/MySQL)...');
    const mysql = require('mysql2/promise');
    sourceDb = await mysql.createConnection(config.source);

    log('Connecting to target database (Node.js/PostgreSQL)...');
    targetDb = new Client(config.target);
    await targetDb.connect();

    log('Database connections established');
  } catch (err) {
    error(`Failed to connect to databases: ${err.message}`);
    process.exit(1);
  }
}

// Close database connections
async function closeConnections() {
  if (sourceDb) await sourceDb.end();
  if (targetDb) await targetDb.end();
  log('Database connections closed');
}

// Migrate companies
async function migrateCompanies() {
  log('Migrating companies...');

  try {
    const [companies] = await sourceDb.execute('SELECT * FROM companies ORDER BY id');

    for (const company of companies) {
      const mappedCompany = {
        id: company.id,
        name: company.name,
        email: company.email,
        phone: company.phone,
        address: company.address,
        city: company.city,
        state: company.state,
        country: company.country,
        postal_code: company.postal_code,
        website: company.website,
        logo: company.logo,
        status: company.status || 'active',
        created_at: company.created_at,
        updated_at: company.updated_at
      };

      await targetDb.execute(
        `INSERT INTO companies (id, name, email, phone, address, city, state, country, postal_code, website, logo, status, created_at, updated_at)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
         ON DUPLICATE KEY UPDATE
         name = VALUES(name), email = VALUES(email), phone = VALUES(phone), updated_at = VALUES(updated_at)`,
        Object.values(mappedCompany)
      );

      stats.companies++;
    }

    log(`Migrated ${stats.companies} companies`);
  } catch (err) {
    error(`Failed to migrate companies: ${err.message}`);
    stats.errors++;
  }
}

// Migrate users
async function migrateUsers() {
  log('Migrating users...');

  try {
    const [users] = await sourceDb.execute('SELECT * FROM users ORDER BY id');

    for (const user of users) {
      // Hash password if it's not already hashed with bcrypt
      let hashedPassword = user.password;
      if (!hashedPassword.startsWith('$2')) {
        hashedPassword = await bcrypt.hash(user.password, 12);
      }

      const mappedUser = {
        id: user.id,
        company_id: user.company_id,
        name: user.name,
        email: user.email,
        password: hashedPassword,
        phone: user.phone,
        role: user.role || 'user',
        status: user.status || 'active',
        email_verified_at: user.email_verified_at,
        created_at: user.created_at,
        updated_at: user.updated_at
      };

      await targetDb.execute(
        `INSERT INTO users (id, company_id, name, email, password, phone, role, status, email_verified_at, created_at, updated_at)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
         ON DUPLICATE KEY UPDATE
         name = VALUES(name), phone = VALUES(phone), role = VALUES(role), status = VALUES(status), updated_at = VALUES(updated_at)`,
        Object.values(mappedUser)
      );

      stats.users++;
    }

    log(`Migrated ${stats.users} users`);
  } catch (err) {
    error(`Failed to migrate users: ${err.message}`);
    stats.errors++;
  }
}

// Migrate customers
async function migrateCustomers() {
  log('Migrating customers...');

  try {
    const [customers] = await sourceDb.execute('SELECT * FROM customers ORDER BY id');

    for (const customer of customers) {
      const mappedCustomer = {
        id: customer.id,
        company_id: customer.company_id,
        name: customer.name,
        email: customer.email,
        phone: customer.phone,
        address: customer.address,
        city: customer.city,
        state: customer.state,
        country: customer.country,
        postal_code: customer.postal_code,
        customer_type: customer.customer_type || 'individual',
        status: customer.status || 'active',
        created_at: customer.created_at,
        updated_at: customer.updated_at
      };

      await targetDb.execute(
        `INSERT INTO customers (id, company_id, name, email, phone, address, city, state, country, postal_code, customer_type, status, created_at, updated_at)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
         ON DUPLICATE KEY UPDATE
         name = VALUES(name), email = VALUES(email), phone = VALUES(phone), updated_at = VALUES(updated_at)`,
        Object.values(mappedCustomer)
      );

      stats.customers++;
    }

    log(`Migrated ${stats.customers} customers`);
  } catch (err) {
    error(`Failed to migrate customers: ${err.message}`);
    stats.errors++;
  }
}

// Migrate products
async function migrateProducts() {
  log('Migrating products...');

  try {
    const [products] = await sourceDb.execute('SELECT * FROM products ORDER BY id');

    for (const product of products) {
      const mappedProduct = {
        id: product.id,
        company_id: product.company_id,
        name: product.name,
        description: product.description,
        sku: product.sku,
        price: product.price,
        cost_price: product.cost_price,
        stock_quantity: product.stock_quantity || 0,
        min_stock_level: product.min_stock_level || 0,
        category_id: product.category_id,
        brand_id: product.brand_id,
        unit_id: product.unit_id,
        status: product.status || 'active',
        created_at: product.created_at,
        updated_at: product.updated_at
      };

      await targetDb.execute(
        `INSERT INTO products (id, company_id, name, description, sku, price, cost_price, stock_quantity, min_stock_level, category_id, brand_id, unit_id, status, created_at, updated_at)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
         ON DUPLICATE KEY UPDATE
         name = VALUES(name), price = VALUES(price), stock_quantity = VALUES(stock_quantity), updated_at = VALUES(updated_at)`,
        Object.values(mappedProduct)
      );

      stats.products++;
    }

    log(`Migrated ${stats.products} products`);
  } catch (err) {
    error(`Failed to migrate products: ${err.message}`);
    stats.errors++;
  }
}

// Migrate sales
async function migrateSales() {
  log('Migrating sales...');

  try {
    const [sales] = await sourceDb.execute('SELECT * FROM sales ORDER BY id');

    for (const sale of sales) {
      const mappedSale = {
        id: sale.id,
        company_id: sale.company_id,
        customer_id: sale.customer_id,
        user_id: sale.user_id,
        sale_number: sale.sale_number,
        sale_date: sale.sale_date,
        subtotal: sale.subtotal,
        tax_amount: sale.tax_amount,
        discount_amount: sale.discount_amount || 0,
        total_amount: sale.total_amount,
        status: sale.status || 'pending',
        notes: sale.notes,
        created_at: sale.created_at,
        updated_at: sale.updated_at
      };

      await targetDb.execute(
        `INSERT INTO sales (id, company_id, customer_id, user_id, sale_number, sale_date, subtotal, tax_amount, discount_amount, total_amount, status, notes, created_at, updated_at)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
         ON DUPLICATE KEY UPDATE
         status = VALUES(status), total_amount = VALUES(total_amount), updated_at = VALUES(updated_at)`,
        Object.values(mappedSale)
      );

      stats.sales++;
    }

    log(`Migrated ${stats.sales} sales`);
  } catch (err) {
    error(`Failed to migrate sales: ${err.message}`);
    stats.errors++;
  }
}

// Generate migration report
async function generateReport() {
  const report = `
# Track New Data Migration Report
Generated: ${new Date().toISOString()}

## Migration Statistics
- Companies: ${stats.companies}
- Users: ${stats.users}
- Customers: ${stats.customers}
- Products: ${stats.products}
- Sales: ${stats.sales}
- Errors: ${stats.errors}

## Source Database
- Host: ${config.source.host}
- Database: ${config.source.database}

## Target Database
- Host: ${config.target.host}
- Database: ${config.target.database}

## Status
${stats.errors === 0 ? '✅ Migration completed successfully' : `⚠️ Migration completed with ${stats.errors} errors`}
`;

  await fs.writeFile('migration-report.md', report);
  log('Migration report saved to migration-report.md');
}

// Main migration function
async function migrate() {
  log('Starting Track New data migration...');

  try {
    await initializeConnections();

    // Run migrations in order
    await migrateCompanies();
    await migrateUsers();
    await migrateCustomers();
    await migrateProducts();
    await migrateSales();

    // Generate report
    await generateReport();

    log('Migration completed successfully!');
    log(`Total records migrated: ${Object.values(stats).reduce((a, b) => a + b, 0) - stats.errors}`);

  } catch (err) {
    error(`Migration failed: ${err.message}`);
    process.exit(1);
  } finally {
    await closeConnections();
  }
}

// Run migration if called directly
if (require.main === module) {
  migrate().catch(err => {
    error(`Unhandled error: ${err.message}`);
    process.exit(1);
  });
}

module.exports = { migrate };
