{"name": "tracknew-nodejs-backend", "version": "1.0.0", "description": "Track New Service Management System - Node.js Backend API", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "migrate": "npx sequelize-cli db:migrate", "migrate:undo": "npx sequelize-cli db:migrate:undo", "seed": "npx sequelize-cli db:seed:all", "seed:undo": "npx sequelize-cli db:seed:undo:all"}, "keywords": ["service-management", "nodejs", "express", "postgresql", "jwt", "api"], "author": "Track New Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "dotenv": "^16.3.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "multer": "^1.4.5-lts.1", "pg": "^8.11.3", "pg-hstore": "^2.3.4", "sequelize": "^6.35.2", "sequelize-cli": "^6.6.2", "nodemailer": "^6.9.8", "twilio": "^4.19.3", "firebase-admin": "^12.0.0", "aws-sdk": "^2.1506.0", "pdf-lib": "^1.17.1", "exceljs": "^4.4.0", "qrcode": "^1.5.3", "moment": "^2.29.4", "lodash": "^4.17.21", "joi": "^17.11.0", "compression": "^1.7.4", "express-slow-down": "^2.0.1", "express-mongo-sanitize": "^2.2.0", "xss-clean": "^0.1.4", "hpp": "^0.2.3", "cookie-parser": "^1.4.6", "express-session": "^1.17.3", "connect-redis": "^7.1.0", "redis": "^4.6.12", "winston": "^3.11.0", "winston-daily-rotate-file": "^4.7.1"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3", "eslint": "^8.56.0", "prettier": "^3.1.1"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}}