#!/bin/bash

# Track New Quick Start Script
# This script sets up Track New for development or production

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Functions
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
    exit 1
}

# Show banner
show_banner() {
    echo -e "${BLUE}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                    🚀 TRACK NEW SETUP                        ║"
    echo "║              Service Management System                       ║"
    echo "║                                                              ║"
    echo "║  Node.js Backend + React Frontend + MySQL + Redis           ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
}

# Check prerequisites
check_prerequisites() {
    log "Checking prerequisites..."
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        error "Docker is not installed. Please install Docker first."
    fi
    
    # Check Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        error "Docker Compose is not installed. Please install Docker Compose first."
    fi
    
    # Check Node.js (for development)
    if [ "$1" = "development" ] && ! command -v node &> /dev/null; then
        warning "Node.js is not installed. Some development features may not work."
    fi
    
    success "Prerequisites check passed"
}

# Setup environment
setup_environment() {
    local env_type=$1
    
    log "Setting up $env_type environment..."
    
    if [ "$env_type" = "development" ]; then
        # Development environment
        if [ ! -f ".env.development" ]; then
            log "Creating development environment file..."
            cat > .env.development << EOF
# Development Environment
NODE_ENV=development
PORT=8000

# Database
DB_HOST=localhost
DB_PORT=3306
DB_NAME=tracknew_development
DB_USER=root
DB_PASSWORD=password
DB_DIALECT=mysql

# JWT
JWT_SECRET=development_jwt_secret_change_in_production
JWT_EXPIRE=7d

# Redis
REDIS_HOST=localhost
REDIS_PORT=6379

# Email (Development)
SMTP_HOST=smtp.mailtrap.io
SMTP_PORT=2525
SMTP_USER=your_mailtrap_user
SMTP_PASS=your_mailtrap_pass

# File Upload
MAX_FILE_SIZE=10485760
UPLOAD_PATH=./uploads

# API Documentation
API_DOCS_ENABLED=true
EOF
            success "Development environment file created"
        fi
        
        # Development Docker Compose
        if [ ! -f "docker-compose.dev.yml" ]; then
            log "Creating development Docker Compose file..."
            cat > docker-compose.dev.yml << EOF
version: '3.8'

services:
  mysql:
    image: mysql:8.0
    container_name: tracknew_mysql_dev
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: password
      MYSQL_DATABASE: tracknew_development
    ports:
      - "3306:3306"
    volumes:
      - mysql_dev_data:/var/lib/mysql

  redis:
    image: redis:7-alpine
    container_name: tracknew_redis_dev
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_dev_data:/data

volumes:
  mysql_dev_data:
  redis_dev_data:
EOF
            success "Development Docker Compose file created"
        fi
        
    else
        # Production environment
        if [ ! -f ".env.production" ]; then
            warning "Production environment file not found. Please configure .env.production manually."
            log "You can use .env.production as a template"
        fi
    fi
}

# Install dependencies
install_dependencies() {
    log "Installing dependencies..."
    
    # Backend dependencies
    if [ -d "tracknew-nodejs-backend" ]; then
        log "Installing backend dependencies..."
        cd tracknew-nodejs-backend
        npm install
        cd ..
        success "Backend dependencies installed"
    fi
    
    # Frontend dependencies
    if [ -d "tracknew-react-frontend" ]; then
        log "Installing frontend dependencies..."
        cd tracknew-react-frontend
        npm install
        cd ..
        success "Frontend dependencies installed"
    fi
}

# Start services
start_services() {
    local env_type=$1
    
    log "Starting $env_type services..."
    
    if [ "$env_type" = "development" ]; then
        # Start development services
        docker-compose -f docker-compose.dev.yml up -d
        
        log "Waiting for services to be ready..."
        sleep 10
        
        # Run database migrations
        cd tracknew-nodejs-backend
        npm run migrate
        
        # Run seeders for development
        npm run seed
        cd ..
        
        success "Development services started"
        log "You can now start the development servers:"
        log "  Backend: cd tracknew-nodejs-backend && npm run dev"
        log "  Frontend: cd tracknew-react-frontend && npm start"
        
    else
        # Start production services
        if [ -f "docker-compose.production.yml" ]; then
            chmod +x deploy.sh
            ./deploy.sh --seed
            success "Production services started"
        else
            error "Production Docker Compose file not found"
        fi
    fi
}

# Show status
show_status() {
    local env_type=$1
    
    log "Service Status:"
    
    if [ "$env_type" = "development" ]; then
        docker-compose -f docker-compose.dev.yml ps
        
        echo ""
        log "Development URLs:"
        log "  Backend API: http://localhost:8000"
        log "  API Docs: http://localhost:8000/api-docs"
        log "  Frontend: http://localhost:3000"
        log "  Database: localhost:3306"
        log "  Redis: localhost:6379"
        
    else
        docker-compose -f docker-compose.production.yml ps
        
        echo ""
        log "Production URLs:"
        log "  Application: https://yourdomain.com"
        log "  API: https://api.yourdomain.com"
        log "  Monitoring: http://yourdomain.com:3001"
    fi
}

# Main function
main() {
    show_banner
    
    # Parse arguments
    ENV_TYPE="development"
    SKIP_DEPS=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --production)
                ENV_TYPE="production"
                shift
                ;;
            --skip-deps)
                SKIP_DEPS=true
                shift
                ;;
            --help)
                echo "Usage: $0 [OPTIONS]"
                echo "Options:"
                echo "  --production    Setup for production environment"
                echo "  --skip-deps     Skip dependency installation"
                echo "  --help          Show this help message"
                exit 0
                ;;
            *)
                error "Unknown option: $1"
                ;;
        esac
    done
    
    log "Setting up Track New for $ENV_TYPE environment..."
    
    # Run setup steps
    check_prerequisites "$ENV_TYPE"
    setup_environment "$ENV_TYPE"
    
    if [ "$SKIP_DEPS" = false ]; then
        install_dependencies
    fi
    
    start_services "$ENV_TYPE"
    show_status "$ENV_TYPE"
    
    success "Track New setup completed!"
    
    if [ "$ENV_TYPE" = "development" ]; then
        log "Next steps:"
        log "1. Start the backend: cd tracknew-nodejs-backend && npm run dev"
        log "2. Start the frontend: cd tracknew-react-frontend && npm start"
        log "3. Open http://localhost:3000 in your browser"
    else
        log "Next steps:"
        log "1. Configure your domain and SSL certificates"
        log "2. Update DNS records to point to your server"
        log "3. Test the application at your domain"
    fi
}

# Run main function
main "$@"
