const express = require('express');
const { body } = require('express-validator');

const purchaseOrderController = require('../controllers/purchaseOrderController');
const { protect, restrictTo, restrictToCompany } = require('../middleware/auth');
const { validateRequest } = require('../middleware/validation');

const router = express.Router();

// Protect all routes
router.use(protect);
router.use(restrictToCompany);

// Validation rules
const createPurchaseOrderValidation = [
  body('supplier_id')
    .isInt({ min: 1 })
    .withMessage('Supplier ID is required and must be a valid number'),
  body('po_date')
    .optional()
    .isISO8601()
    .withMessage('PO date must be a valid date'),
  body('expected_delivery_date')
    .optional()
    .isISO8601()
    .withMessage('Expected delivery date must be a valid date'),
  body('warehouse_id')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Warehouse ID must be a valid number'),
  body('items')
    .isArray({ min: 1 })
    .withMessage('At least one item is required'),
  body('items.*.item_name')
    .trim()
    .isLength({ min: 1, max: 255 })
    .withMessage('Item name is required and must be less than 255 characters'),
  body('items.*.quantity_ordered')
    .isFloat({ min: 0.001 })
    .withMessage('Quantity ordered must be greater than 0'),
  body('items.*.unit_price')
    .isFloat({ min: 0 })
    .withMessage('Unit price must be a positive number'),
  body('items.*.product_id')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Product ID must be a valid number'),
  body('items.*.discount_type')
    .optional()
    .isIn(['percentage', 'fixed'])
    .withMessage('Discount type must be percentage or fixed'),
  body('items.*.discount_value')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Discount value must be a positive number'),
  body('items.*.cgst_rate')
    .optional()
    .isFloat({ min: 0, max: 100 })
    .withMessage('CGST rate must be between 0 and 100'),
  body('items.*.sgst_rate')
    .optional()
    .isFloat({ min: 0, max: 100 })
    .withMessage('SGST rate must be between 0 and 100'),
  body('items.*.igst_rate')
    .optional()
    .isFloat({ min: 0, max: 100 })
    .withMessage('IGST rate must be between 0 and 100'),
  body('discount_type')
    .optional()
    .isIn(['percentage', 'fixed'])
    .withMessage('Discount type must be percentage or fixed'),
  body('discount_value')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Discount value must be a positive number'),
  body('shipping_amount')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Shipping amount must be a positive number'),
  body('other_charges')
    .optional()
    .isFloat()
    .withMessage('Other charges must be a valid number'),
  body('priority')
    .optional()
    .isIn(['low', 'medium', 'high', 'urgent'])
    .withMessage('Priority must be low, medium, high, or urgent'),
  body('payment_terms')
    .optional()
    .trim()
    .isLength({ max: 255 })
    .withMessage('Payment terms must be less than 255 characters'),
  body('delivery_terms')
    .optional()
    .trim()
    .isLength({ max: 255 })
    .withMessage('Delivery terms must be less than 255 characters')
];

const updatePurchaseOrderValidation = [
  body('expected_delivery_date')
    .optional()
    .isISO8601()
    .withMessage('Expected delivery date must be a valid date'),
  body('actual_delivery_date')
    .optional()
    .isISO8601()
    .withMessage('Actual delivery date must be a valid date'),
  body('status')
    .optional()
    .isIn(['draft', 'sent', 'confirmed', 'partially_received', 'received', 'cancelled'])
    .withMessage('Invalid status value'),
  body('payment_status')
    .optional()
    .isIn(['unpaid', 'partially_paid', 'paid', 'overpaid'])
    .withMessage('Invalid payment status value'),
  body('priority')
    .optional()
    .isIn(['low', 'medium', 'high', 'urgent'])
    .withMessage('Priority must be low, medium, high, or urgent'),
  body('other_charges')
    .optional()
    .isFloat()
    .withMessage('Other charges must be a valid number'),
  body('payment_terms')
    .optional()
    .trim()
    .isLength({ max: 255 })
    .withMessage('Payment terms must be less than 255 characters'),
  body('delivery_terms')
    .optional()
    .trim()
    .isLength({ max: 255 })
    .withMessage('Delivery terms must be less than 255 characters'),
  body('terms_conditions')
    .optional()
    .trim()
    .isLength({ max: 5000 })
    .withMessage('Terms and conditions must be less than 5000 characters'),
  body('notes')
    .optional()
    .trim()
    .isLength({ max: 2000 })
    .withMessage('Notes must be less than 2000 characters'),
  body('internal_notes')
    .optional()
    .trim()
    .isLength({ max: 2000 })
    .withMessage('Internal notes must be less than 2000 characters')
];

// Routes
router
  .route('/')
  .get(purchaseOrderController.getPurchaseOrders)
  .post(createPurchaseOrderValidation, validateRequest, purchaseOrderController.createPurchaseOrder);

router
  .route('/:id')
  .get(purchaseOrderController.getPurchaseOrder)
  .put(updatePurchaseOrderValidation, validateRequest, purchaseOrderController.updatePurchaseOrder)
  .delete(restrictTo('admin', 'sub_admin'), purchaseOrderController.deletePurchaseOrder);

module.exports = router;
