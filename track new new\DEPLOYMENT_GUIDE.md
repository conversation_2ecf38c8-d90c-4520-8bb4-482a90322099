# 🚀 Track New Production Deployment Guide

This guide provides step-by-step instructions for deploying Track New to production.

## 📋 Prerequisites

### System Requirements
- **Server**: Ubuntu 20.04+ or CentOS 8+ (minimum 4GB RAM, 2 CPU cores, 50GB storage)
- **Docker**: Version 20.10+
- **Docker Compose**: Version 2.0+
- **Domain**: Registered domain with DNS access
- **SSL Certificate**: Let's Encrypt or commercial SSL certificate

### Required Services
- **PostgreSQL 15+**: Database server
- **Redis 7+**: Caching and session storage
- **Nginx**: Reverse proxy and load balancer
- **Node.js 18+**: Application runtime

## 🔧 Pre-Deployment Setup

### 1. Server Preparation

```bash
# Update system packages
sudo apt update && sudo apt upgrade -y

# Install Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# Install Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# Install additional tools
sudo apt install -y git curl wget unzip htop
```

### 2. Clone Repository

```bash
# Clone the repository
git clone https://github.com/your-org/track-new.git
cd track-new

# Switch to production branch
git checkout main
```

### 3. Environment Configuration

```bash
# Copy environment template
cp .env.production.example .env.production

# Edit environment variables
nano .env.production
```

**Important Environment Variables to Configure:**

```bash
# Database
MYSQL_ROOT_PASSWORD=your_super_secure_root_password
DB_PASSWORD=your_secure_db_password

# Application Secrets
JWT_SECRET=your_super_secure_jwt_secret_minimum_32_characters
SESSION_SECRET=your_session_secret_minimum_32_characters

# Domain Configuration
DOMAIN=yourdomain.com
API_DOMAIN=api.yourdomain.com

# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password

# External Services (Optional)
TWILIO_ACCOUNT_SID=your_twilio_sid
AWS_ACCESS_KEY_ID=your_aws_key
```

## 🚀 Deployment Process

### 1. Initial Deployment

```bash
# Make deployment script executable
chmod +x deploy.sh

# Run initial deployment with database seeding
./deploy.sh --seed

# Check deployment status
docker-compose -f docker-compose.production.yml ps
```

### 2. SSL Certificate Setup

#### Option A: Let's Encrypt (Recommended)

```bash
# Install Certbot
sudo apt install certbot python3-certbot-nginx

# Obtain SSL certificate
sudo certbot --nginx -d yourdomain.com -d www.yourdomain.com -d api.yourdomain.com

# Test automatic renewal
sudo certbot renew --dry-run
```

#### Option B: Custom SSL Certificate

```bash
# Copy your SSL certificates
sudo mkdir -p /opt/tracknew/ssl
sudo cp your_certificate.crt /opt/tracknew/ssl/tracknew.com.crt
sudo cp your_private_key.key /opt/tracknew/ssl/tracknew.com.key
sudo chmod 600 /opt/tracknew/ssl/*
```

### 3. DNS Configuration

Configure your DNS records:

```
A     yourdomain.com        -> YOUR_SERVER_IP
A     www.yourdomain.com    -> YOUR_SERVER_IP
A     api.yourdomain.com    -> YOUR_SERVER_IP
CNAME *.yourdomain.com      -> yourdomain.com
```

### 4. Firewall Configuration

```bash
# Configure UFW firewall
sudo ufw allow ssh
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw enable

# Check firewall status
sudo ufw status
```

## 🔍 Post-Deployment Verification

### 1. Health Checks

```bash
# Check application health
curl -f http://localhost:8000/api/health
curl -f http://localhost:3000/health

# Check SSL certificate
curl -I https://yourdomain.com
curl -I https://api.yourdomain.com
```

### 2. Database Verification

```bash
# Connect to database
docker-compose -f docker-compose.production.yml exec postgres psql -U tracknew_user -d tracknew_production

# Check tables
\dt
\q
```

### 3. Log Monitoring

```bash
# View application logs
docker-compose -f docker-compose.production.yml logs -f backend
docker-compose -f docker-compose.production.yml logs -f frontend
docker-compose -f docker-compose.production.yml logs -f nginx
```

## 🔄 Ongoing Maintenance

### 1. Backup Setup

```bash
# Make backup script executable
chmod +x scripts/backup.sh

# Set up daily backups via cron
sudo crontab -e

# Add this line for daily backups at 2 AM
0 2 * * * /opt/tracknew/scripts/backup.sh
```

### 2. Monitoring Setup

```bash
# Deploy monitoring stack
docker-compose -f monitoring/docker-compose.monitoring.yml up -d

# Access monitoring dashboards
# Grafana: http://yourdomain.com:3001 (admin/admin123)
# Prometheus: http://yourdomain.com:9090
```

### 3. Updates and Maintenance

```bash
# Update application
git pull origin main
./deploy.sh

# Update Docker images
docker-compose -f docker-compose.production.yml pull
docker-compose -f docker-compose.production.yml up -d
```

## 🚨 Troubleshooting

### Common Issues

#### 1. Container Won't Start
```bash
# Check container logs
docker-compose -f docker-compose.production.yml logs [service_name]

# Check container status
docker-compose -f docker-compose.production.yml ps
```

#### 2. Database Connection Issues
```bash
# Check PostgreSQL container
docker-compose -f docker-compose.production.yml exec postgres psql -U postgres -d tracknew_production

# Verify environment variables
docker-compose -f docker-compose.production.yml exec backend env | grep DB_
```

#### 3. SSL Certificate Issues
```bash
# Check certificate validity
openssl x509 -in /opt/tracknew/ssl/tracknew.com.crt -text -noout

# Renew Let's Encrypt certificate
sudo certbot renew
```

#### 4. Performance Issues
```bash
# Check system resources
htop
df -h
docker stats

# Check application metrics
curl http://localhost:8000/api/health
```

## 📊 Performance Optimization

### 1. Database Optimization

```sql
-- Add indexes for better performance (PostgreSQL)
CREATE INDEX CONCURRENTLY idx_customers_company_id ON customers(company_id);
CREATE INDEX CONCURRENTLY idx_sales_created_at ON sales(created_at);
CREATE INDEX CONCURRENTLY idx_invoices_status ON invoices(status);

-- Enable query optimization
ANALYZE;
```

### 2. Nginx Optimization

```nginx
# Add to nginx.conf
worker_processes auto;
worker_connections 2048;

# Enable caching
proxy_cache_path /var/cache/nginx levels=1:2 keys_zone=app_cache:10m max_size=1g inactive=60m;
```

### 3. Application Optimization

```bash
# Enable production optimizations
export NODE_ENV=production
export REDIS_CACHE_TTL=3600
export DB_POOL_MAX=20
```

## 🔐 Security Checklist

- [ ] SSL certificates installed and configured
- [ ] Firewall configured (only ports 22, 80, 443 open)
- [ ] Strong passwords for all services
- [ ] Database access restricted to application only
- [ ] Regular security updates applied
- [ ] Backup encryption enabled
- [ ] Rate limiting configured
- [ ] CORS properly configured
- [ ] Security headers enabled
- [ ] File upload restrictions in place

## 📞 Support

For deployment issues or questions:

1. Check the logs first: `docker-compose logs`
2. Review this guide and troubleshooting section
3. Check GitHub issues: [Repository Issues](https://github.com/your-org/track-new/issues)
4. Contact support: <EMAIL>

## 📝 Additional Resources

- [Docker Documentation](https://docs.docker.com/)
- [Nginx Configuration Guide](https://nginx.org/en/docs/)
- [Let's Encrypt Documentation](https://letsencrypt.org/docs/)
- [MySQL Performance Tuning](https://dev.mysql.com/doc/refman/8.0/en/optimization.html)
