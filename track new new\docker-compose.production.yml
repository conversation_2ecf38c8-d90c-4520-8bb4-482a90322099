version: '3.8'

services:
  # MySQL Database
  mysql:
    image: mysql:8.0
    container_name: tracknew_mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD}
      MYSQL_DATABASE: ${DB_NAME}
      MYSQL_USER: ${DB_USER}
      MYSQL_PASSWORD: ${DB_PASSWORD}
    volumes:
      - mysql_data:/var/lib/mysql
      - ./database/init:/docker-entrypoint-initdb.d
    ports:
      - "3306:3306"
    networks:
      - tracknew_network
    command: --default-authentication-plugin=mysql_native_password

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: tracknew_redis
    restart: unless-stopped
    command: redis-server --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - tracknew_network

  # Node.js Backend API
  backend:
    build:
      context: ./tracknew-nodejs-backend
      dockerfile: Dockerfile
    container_name: tracknew_backend
    restart: unless-stopped
    environment:
      - NODE_ENV=production
    env_file:
      - ./tracknew-nodejs-backend/.env.production
    volumes:
      - backend_uploads:/app/uploads
      - backend_logs:/app/logs
    ports:
      - "8000:8000"
    depends_on:
      - mysql
      - redis
    networks:
      - tracknew_network
    healthcheck:
      test: ["CMD", "node", "healthcheck.js"]
      interval: 30s
      timeout: 10s
      retries: 3

  # React Frontend
  frontend:
    build:
      context: ./tracknew-react-frontend
      dockerfile: Dockerfile
      args:
        - REACT_APP_API_URL=https://api.tracknew.com
        - REACT_APP_ENVIRONMENT=production
    container_name: tracknew_frontend
    restart: unless-stopped
    ports:
      - "3000:80"
    depends_on:
      - backend
    networks:
      - tracknew_network

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: tracknew_nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/conf.d:/etc/nginx/conf.d
      - ./ssl:/etc/nginx/ssl
      - backend_uploads:/var/www/uploads
    depends_on:
      - backend
      - frontend
    networks:
      - tracknew_network

volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local
  backend_uploads:
    driver: local
  backend_logs:
    driver: local

networks:
  tracknew_network:
    driver: bridge
