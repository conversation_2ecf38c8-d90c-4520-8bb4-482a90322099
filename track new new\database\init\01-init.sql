-- Track New Production Database Initialization Script

-- Create database if not exists
CREATE DATABASE IF NOT EXISTS tracknew_production CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Use the database
USE tracknew_production;

-- Create application user with limited privileges
CREATE USER IF NOT EXISTS 'tracknew_user'@'%' IDENTIFIED BY 'your_secure_password_here';

-- Grant necessary privileges
GRANT SELECT, INSERT, UPDATE, DELETE, CREATE, ALTER, INDEX, DROP, REFERENCES ON tracknew_production.* TO 'tracknew_user'@'%';

-- Flush privileges
FLUSH PRIVILEGES;

-- Set timezone
SET time_zone = '+00:00';

-- Enable event scheduler for automated tasks
SET GLOBAL event_scheduler = ON;

-- Create indexes for better performance (will be created by Sequelize migrations)
-- These are additional performance optimizations

-- Performance settings
SET GLOBAL innodb_buffer_pool_size = 1073741824; -- 1GB
SET GLOBAL innodb_log_file_size = 268435456; -- 256MB
SET GLOBAL innodb_flush_log_at_trx_commit = 2;
SET GLOBAL innodb_flush_method = O_DIRECT;

-- Connection settings
SET GLOBAL max_connections = 200;
SET GLOBAL wait_timeout = 28800;
SET GLOBAL interactive_timeout = 28800;

-- Query cache settings
SET GLOBAL query_cache_type = 1;
SET GLOBAL query_cache_size = 67108864; -- 64MB

-- Binary logging for replication (if needed)
-- SET GLOBAL log_bin = ON;
-- SET GLOBAL binlog_format = ROW;

-- Create a sample admin user (password: admin123 - CHANGE IN PRODUCTION!)
-- This will be handled by the application seeder

-- Create backup user for database maintenance
CREATE USER IF NOT EXISTS 'backup_user'@'localhost' IDENTIFIED BY 'backup_password_here';
GRANT SELECT, LOCK TABLES, SHOW VIEW, EVENT, TRIGGER ON tracknew_production.* TO 'backup_user'@'localhost';

-- Create monitoring user for health checks
CREATE USER IF NOT EXISTS 'monitor_user'@'%' IDENTIFIED BY 'monitor_password_here';
GRANT SELECT ON tracknew_production.* TO 'monitor_user'@'%';
GRANT PROCESS ON *.* TO 'monitor_user'@'%';

FLUSH PRIVILEGES;
