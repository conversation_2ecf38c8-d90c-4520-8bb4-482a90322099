# Production Environment Variables for Docker Compose
# IMPORTANT: Change all passwords and secrets before deploying to production!

# PostgreSQL Database Configuration
POSTGRES_PASSWORD=your_super_secure_postgres_password_here
DB_NAME=tracknew_production
DB_USER=tracknew_user
DB_PASSWORD=your_secure_password_here

# Redis Configuration
REDIS_PASSWORD=your_redis_password_here

# Domain Configuration
DOMAIN=tracknew.com
API_DOMAIN=api.tracknew.com

# SSL Configuration
SSL_EMAIL=<EMAIL>

# Backup Configuration
BACKUP_SCHEDULE=0 2 * * *
BAC<PERSON>UP_RETENTION_DAYS=30

# Monitoring Configuration
MONITORING_EMAIL=<EMAIL>

# Application Secrets (Generate new ones for production!)
JWT_SECRET=your_super_secure_jwt_secret_here_minimum_32_characters_long
JWT_REFRESH_SECRET=your_super_secure_refresh_secret_here_minimum_32_characters_long
SESSION_SECRET=your_session_secret_here_minimum_32_characters_long

# External Services
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password

# Twilio
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token
TWILIO_PHONE_NUMBER=your_twilio_phone_number

# AWS
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_REGION=us-east-1
AWS_S3_BUCKET=tracknew-uploads

# Firebase
FIREBASE_PROJECT_ID=your_firebase_project_id
FIREBASE_PRIVATE_KEY_ID=your_firebase_private_key_id
FIREBASE_PRIVATE_KEY=your_firebase_private_key
FIREBASE_CLIENT_EMAIL=your_firebase_client_email
FIREBASE_CLIENT_ID=your_firebase_client_id
