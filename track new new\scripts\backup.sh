#!/bin/bash

# Track New Backup Script
# This script creates backups of database and uploaded files

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
BACKUP_DIR="/opt/tracknew/backups"
RETENTION_DAYS=30
COMPOSE_FILE="docker-compose.production.yml"
DATE=$(date +%Y%m%d_%H%M%S)
LOG_FILE="$BACKUP_DIR/backup_$DATE.log"

# Load environment variables
if [ -f ".env.production" ]; then
    source .env.production
fi

# Functions
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$LOG_FILE"
    exit 1
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$LOG_FILE"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$LOG_FILE"
}

# Create backup directory
create_backup_dir() {
    log "Creating backup directory..."
    mkdir -p "$BACKUP_DIR"
    mkdir -p "$BACKUP_DIR/database"
    mkdir -p "$BACKUP_DIR/uploads"
    mkdir -p "$BACKUP_DIR/logs"
    success "Backup directory created"
}

# Backup database
backup_database() {
    log "Starting database backup..."

    # Check if PostgreSQL container is running
    if ! docker-compose -f "$COMPOSE_FILE" ps postgres | grep -q "Up"; then
        error "PostgreSQL container is not running"
    fi

    # Create database backup
    BACKUP_FILE="$BACKUP_DIR/database/tracknew_db_$DATE.sql"

    docker-compose -f "$COMPOSE_FILE" exec -T postgres pg_dump \
        -U "$DB_USER" \
        -d "$DB_NAME" \
        --clean \
        --if-exists \
        --create \
        --verbose > "$BACKUP_FILE"

    if [ $? -eq 0 ]; then
        # Compress the backup
        gzip "$BACKUP_FILE"
        success "Database backup created: ${BACKUP_FILE}.gz"

        # Calculate backup size
        BACKUP_SIZE=$(du -h "${BACKUP_FILE}.gz" | cut -f1)
        log "Backup size: $BACKUP_SIZE"
    else
        error "Database backup failed"
    fi
}

# Backup uploaded files
backup_uploads() {
    log "Starting uploads backup..."

    # Check if uploads directory exists
    if [ ! -d "./uploads" ]; then
        warning "Uploads directory not found, skipping uploads backup"
        return
    fi

    # Create uploads backup
    UPLOADS_BACKUP="$BACKUP_DIR/uploads/tracknew_uploads_$DATE.tar.gz"

    tar -czf "$UPLOADS_BACKUP" -C . uploads

    if [ $? -eq 0 ]; then
        success "Uploads backup created: $UPLOADS_BACKUP"

        # Calculate backup size
        BACKUP_SIZE=$(du -h "$UPLOADS_BACKUP" | cut -f1)
        log "Uploads backup size: $BACKUP_SIZE"
    else
        error "Uploads backup failed"
    fi
}

# Backup application logs
backup_logs() {
    log "Starting logs backup..."

    # Check if logs directory exists
    if [ ! -d "./logs" ]; then
        warning "Logs directory not found, skipping logs backup"
        return
    fi

    # Create logs backup
    LOGS_BACKUP="$BACKUP_DIR/logs/tracknew_logs_$DATE.tar.gz"

    tar -czf "$LOGS_BACKUP" -C . logs

    if [ $? -eq 0 ]; then
        success "Logs backup created: $LOGS_BACKUP"

        # Calculate backup size
        BACKUP_SIZE=$(du -h "$LOGS_BACKUP" | cut -f1)
        log "Logs backup size: $BACKUP_SIZE"
    else
        warning "Logs backup failed"
    fi
}

# Backup configuration files
backup_config() {
    log "Starting configuration backup..."

    CONFIG_BACKUP="$BACKUP_DIR/tracknew_config_$DATE.tar.gz"

    tar -czf "$CONFIG_BACKUP" \
        --exclude='node_modules' \
        --exclude='.git' \
        --exclude='uploads' \
        --exclude='logs' \
        --exclude='backups' \
        .env.production \
        docker-compose.production.yml \
        nginx/ \
        database/init/ \
        scripts/ \
        2>/dev/null || true

    if [ $? -eq 0 ]; then
        success "Configuration backup created: $CONFIG_BACKUP"
    else
        warning "Configuration backup failed"
    fi
}

# Upload to cloud storage (optional)
upload_to_cloud() {
    if [ -n "$AWS_S3_BUCKET" ] && [ -n "$AWS_ACCESS_KEY_ID" ]; then
        log "Uploading backups to AWS S3..."

        # Upload database backup
        if [ -f "$BACKUP_DIR/database/tracknew_db_$DATE.sql.gz" ]; then
            aws s3 cp "$BACKUP_DIR/database/tracknew_db_$DATE.sql.gz" \
                "s3://$AWS_S3_BUCKET/backups/database/" \
                --storage-class STANDARD_IA
        fi

        # Upload uploads backup
        if [ -f "$BACKUP_DIR/uploads/tracknew_uploads_$DATE.tar.gz" ]; then
            aws s3 cp "$BACKUP_DIR/uploads/tracknew_uploads_$DATE.tar.gz" \
                "s3://$AWS_S3_BUCKET/backups/uploads/" \
                --storage-class STANDARD_IA
        fi

        success "Backups uploaded to S3"
    else
        log "AWS S3 not configured, skipping cloud upload"
    fi
}

# Clean old backups
cleanup_old_backups() {
    log "Cleaning up old backups (older than $RETENTION_DAYS days)..."

    # Clean local backups
    find "$BACKUP_DIR" -type f -mtime +$RETENTION_DAYS -delete

    # Clean S3 backups (if configured)
    if [ -n "$AWS_S3_BUCKET" ] && [ -n "$AWS_ACCESS_KEY_ID" ]; then
        aws s3api list-objects-v2 \
            --bucket "$AWS_S3_BUCKET" \
            --prefix "backups/" \
            --query "Contents[?LastModified<='$(date -d "$RETENTION_DAYS days ago" --iso-8601)'].Key" \
            --output text | xargs -I {} aws s3 rm "s3://$AWS_S3_BUCKET/{}"
    fi

    success "Old backups cleaned up"
}

# Send notification
send_notification() {
    local status=$1
    local message=$2

    if [ -n "$MONITORING_EMAIL" ]; then
        echo "$message" | mail -s "Track New Backup $status" "$MONITORING_EMAIL"
    fi

    # Add Slack notification if webhook is configured
    if [ -n "$SLACK_WEBHOOK_URL" ]; then
        curl -X POST -H 'Content-type: application/json' \
            --data "{\"text\":\"Track New Backup $status: $message\"}" \
            "$SLACK_WEBHOOK_URL"
    fi
}

# Main backup function
main() {
    log "Starting Track New backup process..."

    START_TIME=$(date +%s)

    # Create backup directory
    create_backup_dir

    # Perform backups
    backup_database
    backup_uploads
    backup_logs
    backup_config

    # Upload to cloud (if configured)
    upload_to_cloud

    # Cleanup old backups
    cleanup_old_backups

    END_TIME=$(date +%s)
    DURATION=$((END_TIME - START_TIME))

    success "Backup process completed in ${DURATION} seconds"

    # Send success notification
    send_notification "SUCCESS" "Backup completed successfully in ${DURATION} seconds"
}

# Error handling
trap 'error "Backup process failed"; send_notification "FAILED" "Backup process failed at $(date)"' ERR

# Run main function
main "$@"
